// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'photo_portrait_provide.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$fetchPhotoPortraitBannerHash() =>
    r'c2167b885ff68e3430f17e23588bacf193514b64';

/// See also [fetchPhotoPortraitBanner].
@ProviderFor(fetchPhotoPortraitBanner)
final fetchPhotoPortraitBannerProvider =
    AutoDisposeFutureProvider<List<PhotoPortraitBanner>?>.internal(
  fetchPhotoPortraitBanner,
  name: r'fetchPhotoPortraitBannerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$fetchPhotoPortraitBannerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef FetchPhotoPortraitBannerRef
    = AutoDisposeFutureProviderRef<List<PhotoPortraitBanner>?>;
String _$fetchphotoPortraitCategoryListHash() =>
    r'f8a9d206ade09f68d6eef5a285ddc2814d586f3c';

/// See also [fetchphotoPortraitCategoryList].
@ProviderFor(fetchphotoPortraitCategoryList)
final fetchphotoPortraitCategoryListProvider =
    AutoDisposeFutureProvider<List<PhotoPortraitCategory>?>.internal(
  fetchphotoPortraitCategoryList,
  name: r'fetchphotoPortraitCategoryListProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$fetchphotoPortraitCategoryListHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef FetchphotoPortraitCategoryListRef
    = AutoDisposeFutureProviderRef<List<PhotoPortraitCategory>?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
