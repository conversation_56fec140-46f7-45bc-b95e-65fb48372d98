import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:ms_http/ms_http.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:text_generation_video/app/repository/modals/photo/photo_portrait.dart';
import 'package:text_generation_video/app/repository/service/text_to_video_service.dart';

part 'photo_portrait_provide.g.dart';

@riverpod
Future<List<PhotoPortraitBanner>?> fetchPhotoPortraitBanner(Ref ref) async {
  var result = await TextToVideoService.photoPortraitBanner();
  if (result.status == Status.completed) {
    return result.data;
  }
  return null;
}

@riverpod
Future<List<PhotoPortraitCategory>?> fetchphotoPortraitCategoryList(
    Ref ref) async {
  var result = await TextToVideoService.photoPortraitCategory();
  if (result.status == Status.completed) {
    return result.data;
  }
  return null;
}



/// 真人写真分页列表
/// 页长
// const int pageSize = 10;

// /// 真人写真分页列表结果
// class PhotoPortraitCategoryListResult {
//   final int pageNo;
//   final List<PhotoPortraitCategory?>? categoryList;
//   final LoadState? loadState;

//   const PhotoPortraitCategoryListResult({
//     this.pageNo = 1,
//     this.categoryList,
//     this.loadState,
//   });

//   PhotoPortraitCategoryListResult copyWith({
//     int? page,
//     List<PhotoPortraitCategory?>? categoryList,
//     LoadState? loadState,
//   }) {
//     return PhotoPortraitCategoryListResult(
//       pageNo: page ?? pageNo,
//       categoryList: categoryList ?? this.categoryList,
//       loadState: loadState ?? this.loadState,
//     );
//   }
// }

// @riverpod
// class PhotoPortraitCategoryList extends _$PhotoPortraitCategoryList {
//   @override
//   PhotoPortraitCategoryListResult build() {
//     state = const PhotoPortraitCategoryListResult();
//     loadData();
//     return state;
//   }

//   /// 加载数据
//   void loadData({bool showRefreshToast = false}) async {
//     state = state.copyWith(
//       page: 1,
//       loadState: null,
//     );
//     var result = await TextToVideoService.photoPortraitCategory(
//       pageNum: 1,
//       pageSize: pageSize,
//     );
//     if (result.status == Status.completed) {
//       final pageResponse = result.data;
//       state = state.copyWith(
//         categoryList: pageResponse?.list ?? [],
//         loadState: pageResponse?.hasNextPage == true
//             ? LoadState.idle
//             : LoadState.noMore,
//       );
//     }
//   }

//   /// 加载更多
//   void loadMore() async {
//     if (state.loadState == LoadState.loading ||
//         state.loadState == LoadState.noMore) {
//       return;
//     }

//     state = state.copyWith(
//       loadState: LoadState.loading,
//       page: state.pageNo + 1,
//     );

//     var result = await TextToVideoService.photoPortraitCategory(
//       pageNum: state.pageNo,
//       pageSize: pageSize,
//     );

//     if (result.status == Status.completed) {
//       final pageResponse = result.data;
//       if (pageResponse?.list != null) {
//         state = state.copyWith(
//           categoryList: [...?state.categoryList, ...?pageResponse!.list],
//           loadState:
//               pageResponse.hasNextPage ? LoadState.idle : LoadState.noMore,
//         );
//       } else {
//         state = state.copyWith(
//           loadState: LoadState.noMore,
//         );
//       }
//     } else {
//       // 加载失败，恢复到上一页
//       state = state.copyWith(
//         page: state.pageNo - 1,
//         loadState: LoadState.idle,
//       );
//     }
//   }
// }