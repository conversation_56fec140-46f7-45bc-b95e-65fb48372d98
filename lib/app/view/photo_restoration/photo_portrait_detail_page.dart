import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:text_generation_video/app/repository/modals/photo/photo_portrait.dart';
import 'package:text_generation_video/app/widgets/appbar/leading.dart';
import 'package:text_generation_video/app/widgets/consumption/consumption_display_widget.dart';
import 'package:text_generation_video/config/icon_address.dart';
import 'package:ui_widgets/ui_widgets.dart';

class PhotoPortraitDetailPage extends ConsumerWidget {
  PhotoPortraitDetailPage({super.key, required this.data});

  final PhotoPortraitCategoryDetail data;

  final List<PhotoLocalUrl> imgList = [
    PhotoLocalUrl("https://picsum.photos/200/300?random=9", 0),
    PhotoLocalUrl("https://picsum.photos/200/300?random=9", 1),
    PhotoLocalUrl("https://picsum.photos/200/300?random=9", 2),
    PhotoLocalUrl("https://picsum.photos/200/300?random=9", 3),
    PhotoLocalUrl("https://picsum.photos/200/300?random=9", 4),
    PhotoLocalUrl("https://picsum.photos/200/300?random=9", 5),
    PhotoLocalUrl("https://picsum.photos/200/300?random=9", 6),
    PhotoLocalUrl("https://picsum.photos/200/300?random=9", 7),
  ];

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        centerTitle: true,
        toolbarHeight: 44.h,
        leading: const Leading(
          color: Colors.white,
        ),
        actions: [
          InkWell(
            onTap: () {},
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 5),
              decoration: BoxDecoration(
                color: const Color(0x30FFFFFF),
                borderRadius: BorderRadius.circular(13),
              ),
              child: const Text(
                "生成记录",
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.white,
                ),
              ),
            ),
          ),
          const SizedBox(width: 16),
        ],
      ),
      body: Column(
        children: [
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: SizedBox(
                      width: double.infinity,
                      child: CachedNetworkImage(
                        imageUrl: data.caseImage ?? "",
                        fit: BoxFit.cover,
                        placeholder: (context, url) => Container(
                          color: Colors.grey,
                          child: const Center(
                            child: CircularProgressIndicator(
                              color: Colors.white,
                              strokeWidth: 2,
                            ),
                          ),
                        ),
                        errorWidget: (context, url, error) => Container(
                          color: Colors.grey,
                          child: const Center(
                            child: Icon(
                              Icons.image_not_supported,
                              color: Colors.white54,
                              size: 32,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                  _buildImageSection(data),
                ],
              ),
            ),
          ),
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 16),
            child: ConsumptionDisplayWidget(consumption: 20),
          ),
          GradientButton(
            onPress: () {},
            // enable: false,
            margin: const EdgeInsets.symmetric(horizontal: 16),
            padding: const EdgeInsets.symmetric(vertical: 15),
            radius: 16,
            shadow: false,
            gradient: const LinearGradient(
              colors: [Color(0xFF30E6B8), Color(0xFF30E6B8)],
            ),
            child: const Text(
              "开始改图",
              style: TextStyle(
                fontSize: 14,
                color: Color(0xFF18161A),
              ),
            ),
          ),
          SizedBox(height: MediaQuery.paddingOf(context).bottom + 20),
        ],
      ),
    );
  }

  Widget _buildImageSection(PhotoPortraitCategoryDetail data) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            "请添加一张图片",
            style: TextStyle(
              fontSize: 12,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 12),
          SizedBox(
            height: 65,
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: imgList.length,
              scrollDirection: Axis.horizontal,
              itemBuilder: (context, index) {
                if (index == 0) {
                  return GestureDetector(
                    onTap: () {},
                    child: Container(
                      padding: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(15),
                          color: const Color(0xFF2D2C2F)),
                      child: Image.asset(modificationSelectIcon),
                    ),
                  );
                }
                final item = imgList[index];
                return _buildImageItem(item.url ?? "",
                     item.index == 1);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildImageItem(String imageUrl, bool isSelected) {
    return Container(
      margin: const EdgeInsets.only(left: 4),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(15),
        border: Border.all(
          color: isSelected ? const Color(0xFF30E6B8) : Colors.transparent,
          width: 2,
        ),
      ),
      child: SizedBox(
          child: ClipRRect(
        borderRadius: BorderRadius.circular(isSelected ? 13 : 15),
        child: CachedNetworkImage(
          width: 65,
          imageUrl: imageUrl,
          fit: BoxFit.cover,
          placeholder: (context, url) => Container(
            color: Colors.grey,
            child: const Center(
              child: CircularProgressIndicator(
                color: Colors.white,
                strokeWidth: 2,
              ),
            ),
          ),
          errorWidget: (context, url, error) => Container(
            color: Colors.grey,
            child: const Center(
              child: Icon(
                Icons.image_not_supported,
                color: Colors.white54,
                size: 32,
              ),
            ),
          ),
        ),
      )),
    );
  }
}

class PhotoLocalUrl {
  final String? url;
  final int index;

  PhotoLocalUrl(this.url, this.index);
}
